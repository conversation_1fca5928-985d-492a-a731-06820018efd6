# 以太坊主网交互任务自动化系统

## 项目简介

本系统是一个用于自动化执行以太坊主网任务的工具，支持多种任务类型，如质押ETH到Lido和EigenLayer、Aave存款、Arbitrum跨链、WETH兑换以及NFT铸造等。系统采用集成式设计，所有功能都在单一文件中实现，便于部署和使用。

## 目录结构

```
project/
├── run.py                # 主程序文件
├── config.json           # 配置文件
├── 说明.txt              # 说明文档
├── wallet_用户名.txt     # 钱包私钥文件
└── logs/                 # 日志目录
    ├── 用户名/           # 用户特定日志目录
    │   └── run.log       # 运行日志
    └── 项目名称/         # 项目特定目录
        └── 用户名/       # 项目下的用户特定目录
            ├── 成功_项目名称.txt  # 成功执行记录
            └── 失败_项目名称.txt  # 失败执行记录
```

## 功能特点

1. **集成式设计**：所有功能在单一脚本中实现，无需额外模块
2. **多项目支持**：支持多种不同的以太坊交互任务
3. **健壮的Web3连接**：支持多RPC节点，自动故障切换
4. **任务队列管理**：智能调度任务，支持并发执行
5. **gas监控系统**：自动监控gas价格，当价格过高时暂停任务
6. **灵活的配置**：通过配置文件调整各项目参数
7. **自动恢复机制**：交易失败可以自动重试
8. **详细的日志记录**：全面记录交易执行情况
9. **测试模式**：支持单钱包测试特定项目

## 支持的项目

### 1. Lido和EigenLayer质押（lido_eigenlayer_stak）
将ETH质押到Lido获取stETH，然后将stETH质押到EigenLayer。

### 2. Aave存款（aave_supply）
向Aave协议存入ETH，获取aETH。

### 3. Arbitrum跨链（arbitrum_bridge）
将ETH从以太坊主网跨链到Arbitrum网络。

### 4. WETH兑换（weth_deposit）
存入ETH兑换为WETH（包装的ETH）。

### 5. NFT铸造 - EternalEchoes（mint_nft_eternalechoes）
铸造EternalEchoes项目的NFT，使用mintPublic函数，随机铸造5-10个NFT到当前钱包地址。

### 6. NFT铸造 - CARELLA（mint_nft_carella）
铸造CARELLA项目的NFT，使用purchase函数调用，铸造单个NFT到当前钱包地址。

### 7. NFT铸造 - AAAA（mint_nft_aaaa）
铸造AAAA项目的NFT，使用mintPublic函数，随机铸造5-10个NFT到当前钱包地址。

### 8. NFT铸造 - Runtomoon（mint_nft_runtomoon）
铸造Runtomoon项目的NFT，使用mintPublic函数，随机铸造5-10个NFT到当前钱包地址。

### 9. USDT授权 - Aggregation（approve_usdt_aggregation）
对USDT合约授权，允许1inch聚合器使用指定数量的USDT代币。授权数量随机在1000000000-10000000000之间，且尾数有6个0。

### 10. USDT授权 - Uniswap（approve_usdt_uniswap）
对USDT合约授权，允许Uniswap路由器使用指定数量的USDT代币。授权数量随机在1000000000-10000000000之间，且尾数有6个0。

### 11. Blur存款（blur_deposit）
向Blur交易所存入ETH，使用deposit函数，发送金额随机在0.00005-0.0001 ETH之间（保留6位小数）。

### 12. NFT铸造 - RBVRS（mint_nft_rbvrs）
通过RBVRS合约铸造NFT，调用mintPublic函数，需要指定NFT合约地址、费用接收地址、铸造者地址以及铸造数量。铸造数量随机在3-5个之间，铸造者地址使用当前钱包地址。

## 配置文件说明

配置文件`config.json`包含以下主要配置项：

```json
{
  "rpc_urls": [
    "https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca"
  ],
  "query_rpc": "https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca",
  "enabled_projects": {
    "lido_eigenlayer_stak": true,
    "aave_supply": true,
    "arbitrum_bridge": true,
    "weth_deposit": true,
    "mint_nft_eternalechoes": true,
    "mint_nft_carella": true,
    "mint_nft_aaaa": true,
    "mint_nft_runtomoon": true,
    "approve_usdt_aggregation": true,
    "approve_usdt_uniswap": true,
    "blur_deposit": true,
    "mint_nft_rbvrs": true
  },
  "project_settings": {
    "lido_eigenlayer_stak": {
      "lido_contract": "******************************************",
      "eigenlayer_contract": "******************************************",
      "referral": "******************************************",
      "strategy": "******************************************",
      "min_amount": "0.001",
      "max_amount": "0.0015",
      "use_query_rpc": false
    },
    "aave_supply": {
      "contract_address": "******************************************",
      "pool_address": "******************************************",
      "min_amount": "0.00005",
      "max_amount": "0.0001",
      "use_query_rpc": false
    },
    "arbitrum_bridge": {
      "bridge_contract": "******************************************",
      "min_amount": "0.00005",
      "max_amount": "0.0001",
      "use_query_rpc": false
    },
    "weth_deposit": {
      "weth_contract": "******************************************",
      "min_amount": "0.00005",
      "max_amount": "0.0001",
      "use_query_rpc": false
    },
    "mint_nft_eternalechoes": {
      "nft_contract": "******************************************",
      "use_query_rpc": false
    },
    "mint_nft_carella": {
      "nft_contract": "******************************************",
      "use_query_rpc": false
    },
    "mint_nft_aaaa": {
      "nft_contract": "******************************************",
      "use_query_rpc": false
    },
    "mint_nft_runtomoon": {
      "nft_contract": "******************************************",
      "use_query_rpc": false
    },
    "approve_usdt_aggregation": {
      "usdt_contract": "******************************************",
      "spender": "******************************************",
      "use_query_rpc": false
    },
    "approve_usdt_uniswap": {
      "usdt_contract": "******************************************",
      "spender": "0x66a9893cC07D91D95644AEDD05D03f95e1dBA8Af",
      "use_query_rpc": false
    },
    "blur_deposit": {
      "blur_contract": "0x0000000000A39bb272e79075ade125fd351887Ac",
      "min_amount": "0.00005",
      "max_amount": "0.0001",
      "use_query_rpc": false
    },
    "mint_nft_rbvrs": {
      "rbvrs_contract": "******************************************",
      "nft_contract": "******************************************",
      "fee_recipient": "******************************************",
      "use_query_rpc": false
    }
  }
}
```

- `rpc_urls`: RPC节点列表，用于连接以太坊网络
- `query_rpc`: 专用于查询的RPC节点
- `enabled_projects`: 启用的项目任务
- `project_settings`: 各项目的具体配置参数

## 使用方法

### 安装依赖

```bash
pip install web3 eth-account aiofiles
```

### 准备钱包文件

创建`wallet_用户名.txt`文件，每行一个私钥：

```
0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
0x2345678901abcdef2345678901abcdef2345678901abcdef2345678901abcdef
```

### 运行系统

```bash
python run.py
```

### 操作流程

1. **输入用户名**：系统会读取对应的`wallet_用户名.txt`文件
2. **选择运行模式**：
   - 正常模式：批量处理多个钱包
   - 测试模式：选择单个钱包和项目进行测试
3. **正常模式设置**：
   - 任务模式：完成所有未完成任务/随机完成1个未完成任务/随机完成2个未完成任务/随机完成3个未完成任务
   - 并发数：同时执行的任务数量
   - gas阈值：当gas价格超过此值时暂停任务
   - 最低gas阈值：当gas低于此值时，使用此阈值（可选）
4. **测试模式设置**：
   - 选择测试钱包
   - 选择测试项目
5. **系统执行**：
   - 分析待处理任务
   - 启动gas监控
   - 执行任务队列
   - 实时显示进度

## 系统架构

系统由以下主要类组成：

1. **WalletInfo**：钱包信息管理
2. **Web3Pool**：Web3连接池，管理多个RPC连接
3. **ProjectBase**：项目基类，提供通用方法
4. **具体项目类**：继承自ProjectBase，实现特定项目逻辑
   - LidoEigenLayerStakProject
   - AaveSupplyProject
   - ArbitrumBridgeProject
   - WethDepositProject
5. **TaskQueue**：任务队列，管理任务执行
6. **ImprovedProjectManager**：项目管理器，协调整个系统

## 执行流程

1. 初始化Web3连接池
2. 加载钱包和配置
3. 分析待完成任务
4. 启动gas监控
5. 并发执行任务
6. 保存执行结果
7. 显示执行统计

## 注意事项

1. **钱包安全**：使用前请备份私钥，确保钱包安全
2. **测试模式**：测试模式会执行真实交易，请谨慎使用
3. **交易记录**：系统会自动记录已完成的任务，避免重复执行
4. **交易费用**：系统会自动调整gas费用，默认会在当前市场价基础上增加10%
5. **gas控制**：当gas价格超过阈值时，系统会自动暂停任务，等到价格恢复正常后继续执行
6. **并发限制**：合理设置并发数，过高的并发可能导致交易失败率增加
7. **项目文件夹**：系统会自动创建项目文件夹用于存储结果

## 错误处理

系统内置了多种错误处理机制：

1. **交易重试**：交易失败会尝试重新发送
2. **nonce修正**：自动处理nonce错误
3. **RPC故障切换**：当RPC连接失败时会自动切换到其他节点
4. **详细日志**：记录所有执行步骤和错误