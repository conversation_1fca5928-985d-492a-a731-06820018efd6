<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .project-card {
            cursor: pointer;
            transition: transform 0.2s;
        }
        .project-card:hover {
            transform: translateY(-5px);
        }
        .address-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .stats-card {
            height: 100%;
        }
        .chart-container {
            height: 400px;
        }
        .address-item {
            word-break: break-all;
        }
        .action-buttons {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand mb-0 h1">任务分析系统</span>
            <div class="d-flex">
                <input type="text" id="username" class="form-control me-2" placeholder="输入用户名">
                <button class="btn btn-outline-light" onclick="loadUser()">加载数据</button>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 基础统计信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card stats-card">
                    <div class="card-body">
                        <h5 class="card-title">基础统计</h5>
                        <div class="row">
                            <div class="col-6">
                                <p class="mb-1">总钱包数量</p>
                                <h3 id="total-wallets">-</h3>
                            </div>
                            <div class="col-6">
                                <p class="mb-1">总项目数量</p>
                                <h3 id="total-projects">-</h3>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <p class="mb-1 text-success">完成任务钱包数</p>
                                <h3 id="total-success-wallets" class="text-success">-</h3>
                            </div>
                            <div class="col-6">
                                <p class="mb-1 text-danger">失败任务钱包数</p>
                                <h3 id="total-failed-wallets" class="text-danger">-</h3>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-warning btn-sm me-2" onclick="cleanFailedFiles()">
                                <i class="bi bi-trash"></i> 清理失败文件
                            </button>
                            <button class="btn btn-info btn-sm" onclick="exportUnusedWallets()">
                                <i class="bi bi-download"></i> 导出未使用钱包
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card stats-card">
                    <div class="card-body">
                        <h5 class="card-title">地址查询</h5>
                        <div class="input-group">
                            <input type="text" id="address-search" class="form-control" placeholder="输入钱包地址">
                            <button class="btn btn-primary" onclick="searchAddress()">
                                <i class="bi bi-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目统计图表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">项目统计</h5>
                        <div id="projects-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目列表和详情 -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">项目列表</h5>
                        <div id="projects-list" class="row g-4">
                            <!-- 项目卡片将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">详细信息</h5>
                        <div id="detail-panel">
                            <p class="text-muted">选择项目或搜索地址查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script>
        let currentStats = null;

        async function loadUser() {
            const username = document.getElementById('username').value;
            if (!username) {
                alert('请输入用户名');
                return;
            }

            try {
                const response = await fetch('/load_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}`
                });
                const data = await response.json();
                
                if (data.success) {
                    loadStats();
                } else {
                    alert(data.error || '加载失败');
                }
            } catch (error) {
                alert('加载失败: ' + error);
            }
        }

        async function loadStats() {
            const response = await fetch('/get_stats');
            currentStats = await response.json();
            updateUI(currentStats);
        }

        async function cleanFailedFiles() {
            if (!confirm('确定要清理所有失败文件吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/clean_failed_files', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert(result.message);
                    // 重新加载统计数据
                    loadStats();
                } else {
                    alert(result.message || '清理失败');
                }
            } catch (error) {
                alert('清理失败: ' + error);
            }
        }

        async function exportUnusedWallets() {
            try {
                window.location.href = '/export_unused_wallets';
            } catch (error) {
                alert('导出失败: ' + error);
            }
        }

        function updateUI(stats) {
            // 更新基础统计
            document.getElementById('total-wallets').textContent = stats.total_wallets;
            document.getElementById('total-projects').textContent = stats.total_projects;
            document.getElementById('total-success-wallets').textContent = stats.total_success_wallets;
            document.getElementById('total-failed-wallets').textContent = stats.total_failed_wallets;

            // 更新项目列表
            const projectsList = document.getElementById('projects-list');
            projectsList.innerHTML = '';
            
            Object.entries(stats.project_stats).forEach(([project, data]) => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4';
                card.innerHTML = `
                    <div class="card project-card" onclick="showProjectDetails('${project}')">
                        <div class="card-body">
                            <h5 class="card-title">${project}</h5>
                            <p class="card-text">
                                成功率: ${(data.success_rate * 100).toFixed(2)}%<br>
                                成功: ${data.successful_attempts}<br>
                                失败: ${data.failed_attempts}
                            </p>
                        </div>
                    </div>
                `;
                projectsList.appendChild(card);
            });

            // 更新图表
            updateChart(stats);
        }

        function updateChart(stats) {
            const projects = Object.keys(stats.project_stats);
            const successRates = projects.map(p => stats.project_stats[p].success_rate * 100);
            const successCounts = projects.map(p => stats.project_stats[p].successful_attempts);
            const failedCounts = projects.map(p => stats.project_stats[p].failed_attempts);

            const trace1 = {
                x: projects,
                y: successRates,
                name: '成功率',
                type: 'scatter',
                yaxis: 'y2',
                line: {color: '#2ecc71'}
            };

            const trace2 = {
                x: projects,
                y: successCounts,
                name: '成功数',
                type: 'bar',
                marker: {color: '#3498db'}
            };

            const trace3 = {
                x: projects,
                y: failedCounts,
                name: '失败数',
                type: 'bar',
                marker: {color: '#e74c3c'}
            };

            const layout = {
                barmode: 'group',
                yaxis: {title: '数量'},
                yaxis2: {
                    title: '成功率 (%)',
                    overlaying: 'y',
                    side: 'right',
                    range: [0, 100]
                },
                legend: {orientation: 'h'},
                margin: {t: 20}
            };

            Plotly.newPlot('projects-chart', [trace1, trace2, trace3], layout);
        }

        async function showProjectDetails(project) {
            const [successResponse, failedResponse] = await Promise.all([
                fetch(`/get_project_addresses/${project}/success`),
                fetch(`/get_project_addresses/${project}/failed`)
            ]);

            const successAddresses = await successResponse.json();
            const failedAddresses = await failedResponse.json();

            const detailPanel = document.getElementById('detail-panel');
            detailPanel.innerHTML = `
                <h6 class="mb-3">${project}</h6>
                <div class="mb-3">
                    <h6 class="text-success">成功地址 (${successAddresses.length})</h6>
                    <div class="address-list border rounded p-2">
                        ${successAddresses.map(addr => `
                            <div class="address-item mb-1">
                                <small class="text-muted">${addr}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div>
                    <h6 class="text-danger">失败地址 (${failedAddresses.length})</h6>
                    <div class="address-list border rounded p-2">
                        ${failedAddresses.map(addr => `
                            <div class="address-item mb-1">
                                <small class="text-muted">${addr}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        async function searchAddress() {
            const address = document.getElementById('address-search').value;
            if (!address) {
                alert('请输入钱包地址');
                return;
            }

            const response = await fetch(`/get_address_stats/${address}`);
            const stats = await response.json();

            const detailPanel = document.getElementById('detail-panel');
            detailPanel.innerHTML = `
                <h6 class="mb-3">地址详情</h6>
                <p class="text-muted small">${address}</p>
                <div class="mb-3">
                    <p>完成率: ${stats.completion_rate.toFixed(2)}%</p>
                    <p>已完成: ${stats.completed_projects.length} / ${stats.total_projects}</p>
                </div>
                <div class="mb-3">
                    <h6 class="text-success">已完成项目 (${stats.completed_projects.length})</h6>
                    <div class="address-list border rounded p-2">
                        ${stats.completed_projects.map(project => `
                            <div class="mb-1">
                                <small class="text-muted">${project}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div>
                    <h6 class="text-danger">失败项目 (${stats.failed_projects.length})</h6>
                    <div class="address-list border rounded p-2">
                        ${stats.failed_projects.map(project => `
                            <div class="mb-1">
                                <small class="text-muted">${project}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html> 