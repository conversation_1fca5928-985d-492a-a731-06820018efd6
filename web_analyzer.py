import os
import json
from web3 import Web3
import pandas as pd
from datetime import datetime
from collections import defaultdict
from flask import Flask, render_template, jsonify, request, send_file, Response
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.utils
import webbrowser
from threading import Timer
import io

class LogAnalyzer:
    def __init__(self):
        self.w3 = Web3()
        self.logs_dir = "logs"
        self.wallet_address_map = {}  # 私钥到地址的映射
        self.project_stats = defaultdict(lambda: defaultdict(dict))  # 项目统计数据
        self.reverse_wallet_map = {}  # 地址到私钥的反向映射
        self.current_username = None  # 当前加载的用户名
        
    def load_wallet_addresses(self, username):
        """加载用户钱包文件并解析私钥到地址"""
        self.current_username = username
        wallet_file = f"wallet_{username}.txt"
        try:
            with open(wallet_file, 'r') as f:
                private_keys = [line.strip() for line in f.readlines() if line.strip()]
                for pk in private_keys:
                    try:
                        account = self.w3.eth.account.from_key(pk)
                        self.wallet_address_map[pk] = account.address
                        self.reverse_wallet_map[account.address] = pk
                    except Exception as e:
                        print(f"无法解析私钥: {e}")
            return True
        except FileNotFoundError:
            print(f"找不到钱包文件: {wallet_file}")
            return False

    def scan_project_logs(self, username):
        """扫描项目日志文件"""
        user_logs_path = os.path.join(self.logs_dir)
        for project_name in os.listdir(user_logs_path):
            project_path = os.path.join(user_logs_path, project_name, username)
            if not os.path.exists(project_path):
                continue

            success_file = os.path.join(project_path, f"成功_{project_name}.txt")
            failed_file = os.path.join(project_path, f"失败_{project_name}.txt")

            success_keys = self._read_keys_from_file(success_file)
            failed_keys = self._read_keys_from_file(failed_file)

            for pk in success_keys:
                if pk in self.wallet_address_map:
                    addr = self.wallet_address_map[pk]
                    self.project_stats[project_name][addr]['success'] = True

            for pk in failed_keys:
                if pk in self.wallet_address_map:
                    addr = self.wallet_address_map[pk]
                    self.project_stats[project_name][addr]['failed'] = True

    def _read_keys_from_file(self, filepath):
        if not os.path.exists(filepath):
            return set()
        try:
            with open(filepath, 'r') as f:
                return {line.strip() for line in f.readlines() if line.strip()}
        except Exception as e:
            print(f"读取文件出错 {filepath}: {e}")
            return set()

    def get_project_addresses(self, project_name, status='success'):
        """获取特定项目的地址列表"""
        addresses = []
        if project_name in self.project_stats:
            for addr, stats in self.project_stats[project_name].items():
                if stats.get(status, False):
                    addresses.append(addr)
        return addresses

    def get_address_stats(self, address):
        """获取特定地址的任务完成情况"""
        stats = {
            'address': address,
            'completed_projects': [],
            'failed_projects': [],
            'total_projects': len(self.project_stats),
            'completion_rate': 0
        }
        
        completed_count = 0
        for project, addresses in self.project_stats.items():
            if address in addresses:
                if addresses[address].get('success', False):
                    completed_count += 1
                    stats['completed_projects'].append(project)
                if addresses[address].get('failed', False):
                    stats['failed_projects'].append(project)
        
        stats['completion_rate'] = (completed_count / stats['total_projects']) * 100 if stats['total_projects'] > 0 else 0
        return stats

    def get_all_stats(self):
        """获取所有统计数据"""
        project_stats = {}
        # 用于统计总体情况的集合
        total_success_addresses = set()
        total_failed_addresses = set()
        
        for project, addresses in self.project_stats.items():
            total = len(addresses)
            success = sum(1 for addr_stats in addresses.values() if addr_stats.get('success', False))
            failed = sum(1 for addr_stats in addresses.values() if addr_stats.get('failed', False))
            
            # 收集成功和失败的地址
            for addr, stats in addresses.items():
                if stats.get('success', False):
                    total_success_addresses.add(addr)
                if stats.get('failed', False):
                    total_failed_addresses.add(addr)
            
            project_stats[project] = {
                'success_rate': success / total if total > 0 else 0,
                'total_attempts': total,
                'successful_attempts': success,
                'failed_attempts': failed
            }
        
        return {
            'total_wallets': len(self.wallet_address_map),
            'total_projects': len(self.project_stats),
            'total_success_wallets': len(total_success_addresses),
            'total_failed_wallets': len(total_failed_addresses),
            'project_stats': project_stats
        }

    def clean_failed_files(self):
        """清理所有失败文件"""
        if not self.current_username:
            return {"success": False, "message": "请先加载用户数据"}

        cleaned_files = []
        try:
            for project_name in os.listdir(self.logs_dir):
                project_path = os.path.join(self.logs_dir, project_name, self.current_username)
                if not os.path.exists(project_path):
                    continue

                failed_file = os.path.join(project_path, f"失败_{project_name}.txt")
                if os.path.exists(failed_file):
                    os.remove(failed_file)
                    cleaned_files.append(project_name)
                    # 清除该项目的失败记录
                    if project_name in self.project_stats:
                        for addr in list(self.project_stats[project_name].keys()):
                            if self.project_stats[project_name][addr].get('failed', False):
                                # 如果只有失败记录，删除整个地址记录
                                if not self.project_stats[project_name][addr].get('success', False):
                                    del self.project_stats[project_name][addr]
                                else:
                                    # 如果还有成功记录，只删除失败标记
                                    del self.project_stats[project_name][addr]['failed']

            # 重新扫描日志文件以确保数据同步
            self.project_stats.clear()
            self.scan_project_logs(self.current_username)

            return {
                "success": True,
                "message": f"成功清理 {len(cleaned_files)} 个失败文件",
                "cleaned_files": cleaned_files
            }
        except Exception as e:
            return {"success": False, "message": f"清理失败: {str(e)}"}

    def get_unused_wallets(self):
        """获取未使用过的钱包私钥"""
        used_addresses = set()
        
        # 收集所有参与过任务的地址
        for project, addresses in self.project_stats.items():
            for addr in addresses.keys():
                used_addresses.add(addr)
        
        # 找出未使用的私钥
        unused_keys = []
        for pk, addr in self.wallet_address_map.items():
            if addr not in used_addresses:
                unused_keys.append(pk)
        
        return unused_keys

app = Flask(__name__)
analyzer = LogAnalyzer()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/load_user', methods=['POST'])
def load_user():
    username = request.form.get('username')
    if analyzer.load_wallet_addresses(username):
        analyzer.scan_project_logs(username)
        return jsonify({'success': True})
    return jsonify({'success': False, 'error': '无法加载用户数据'})

@app.route('/get_stats')
def get_stats():
    return jsonify(analyzer.get_all_stats())

@app.route('/get_project_addresses/<project_name>/<status>')
def get_project_addresses(project_name, status):
    addresses = analyzer.get_project_addresses(project_name, status)
    return jsonify(addresses)

@app.route('/get_address_stats/<address>')
def get_address_stats(address):
    stats = analyzer.get_address_stats(address)
    return jsonify(stats)

@app.route('/clean_failed_files', methods=['POST'])
def clean_failed_files():
    result = analyzer.clean_failed_files()
    return jsonify(result)

@app.route('/export_unused_wallets')
def export_unused_wallets():
    unused_keys = analyzer.get_unused_wallets()
    if not unused_keys:
        return jsonify({"success": False, "message": "没有找到未使用的钱包"})
    
    # 创建内存文件
    output = io.StringIO()
    for key in unused_keys:
        output.write(f"{key}\n")
    
    # 创建响应
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output.seek(0)
    return Response(
        output.getvalue(),
        mimetype='text/plain',
        headers={
            "Content-Disposition": f"attachment; filename=unused_wallets_{timestamp}.txt"
        }
    )

def open_browser():
    webbrowser.open('http://127.0.0.1:5000/')

if __name__ == '__main__':
    Timer(1, open_browser).start()
    app.run(debug=True) 