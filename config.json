{"rpc_urls": ["https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca"], "query_rpc": "https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca", "enabled_projects": {"lido_eigenlayer_stak": false, "aave_supply": false, "arbitrum_bridge": false, "weth_deposit": false, "mint_nft_eternalechoes": false, "mint_nft_carella": false, "mint_nft_aaaa": false, "mint_nft_runtomoon": false, "approve_usdt_aggregation": false, "approve_usdt_uniswap": false, "blur_deposit": false, "mint_nft_rbvrs": false, "mint_nft_poply": false, "mint_nft_10yeth": true}, "project_settings": {"lido_eigenlayer_stak": {"lido_contract": "******************************************", "eigenlayer_contract": "******************************************", "referral": "******************************************", "strategy": "******************************************", "min_amount": "0.0001", "max_amount": "0.00015", "use_query_rpc": false}, "aave_supply": {"contract_address": "******************************************", "pool_address": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "arbitrum_bridge": {"bridge_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "weth_deposit": {"weth_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "mint_nft_eternalechoes": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_carella": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_aaaa": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_runtomoon": {"nft_contract": "******************************************", "use_query_rpc": false}, "approve_usdt_aggregation": {"usdt_contract": "******************************************", "spender": "******************************************", "use_query_rpc": false}, "approve_usdt_uniswap": {"usdt_contract": "******************************************", "spender": "******************************************", "use_query_rpc": false}, "blur_deposit": {"blur_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "mint_nft_rbvrs": {"rbvrs_contract": "******************************************", "nft_contract": "******************************************", "fee_recipient": "******************************************", "use_query_rpc": false}, "mint_nft_poply": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_10yeth": {"nft_contract": "******************************************", "transaction_data": "0x1249c58b", "use_query_rpc": false}}}